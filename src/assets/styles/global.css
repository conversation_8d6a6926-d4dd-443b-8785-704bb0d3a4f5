/* Global Styles - Standardized styling for the entire application */

/* Custom Properties (CSS Variables) for theming */
:root {
  /* Primary Colors */
  --color-primary: #FFD700; /* Changed from #42b883 (green) to #FFD700 (gold) */
  --color-primary-light: #FFE44D; /* Lighter yellow */
  --color-primary-dark: #E6C200; /* Darker yellow */
  
  /* Secondary Colors */
  --color-secondary: #35495e;
  --color-secondary-light: #4e6882;
  --color-secondary-dark: #2c3e50;
  
  /* Neutral Colors */
  --color-text-dark: #333333;
  --color-text-light: #cfcfcf;
  --color-text-lighter: #ebe8e8;
  --color-background: #ffffff;
  --color-background-alt: #f5f5f5;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
  
  /* Typography */
  --font-family-main: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
  --font-family-heading: var(--font-family-main);
  --font-size-base: 16px;
  --line-height-base: 1.6;
  
  /* Border Radius */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  
  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  
  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  font-family: var(--font-family-main);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  color: var(--color-text-dark);
  background-color: var(--color-background);
}

/* Base Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-heading);
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  line-height: 1.2;
  color: var(--color-text-dark);
}

h1 {
  font-size: 2.5rem;
}

h2 {
  font-size: 2rem;
}

h3 {
  font-size: 1.5rem;
}

h4 {
  font-size: 1.25rem;
}

p {
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  line-height: var(--line-height-base);
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-primary-dark);
}

img {
  max-width: 100%;
  height: auto;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  font-family: var(--font-family-main);
  font-size: 1rem;
  text-align: center;
  text-decoration: none;
  transition: background-color var(--transition-fast);
}

.btn:hover {
  background-color: var(--color-primary-dark);
}

.btn-secondary {
  background-color: var(--color-secondary);
}

.btn-secondary:hover {
  background-color: var(--color-secondary-dark);
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--color-primary);
  color: var(--color-primary);
}

.btn-outline:hover {
  background-color: var(--color-primary);
  color: white;
}

/* Cards */
.card {
  background-color: var(--color-background);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.card-content {
  padding: var(--spacing-md);
}

/* Grid Layout */
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-lg);
}

/* Section Styling */
.section {
  padding: var(--spacing-xl) 0;
}

.section-title {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.text-primary {
  color: var(--color-primary);
}

.text-secondary {
  color: var(--color-secondary);
}

.bg-alt {
  background-color: var(--color-background-alt);
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  h1 {
    font-size: 2rem;
  }
  
  h2 {
    font-size: 1.75rem;
  }
  
  .section {
    padding: var(--spacing-lg) 0;
  }
}

