<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import appData from '@/stores/appData.js'

const router = useRouter()
// Access the shared data
const { state } = appData
const artist = state.artist
const featuredWorks = ref(state.portfolioItems.slice(0, 3)) // Just show first 3 items

const navigateTo = (path) => {
  router.push(path)
}
</script>

<template>
  <div class="home">
    <section class="hero section">
      <div class="container">
        <div class="hero-content">
          <h1 class="hero-title">{{ artist.name }}</h1>
          <h2 class="hero-subtitle">{{ artist.title }}</h2>
          <p class="hero-text">{{ artist.bio }}</p>
          <div class="hero-buttons">
            <button @click="navigateTo('/portfolio')" class="btn">View Portfolio</button>
            <button @click="navigateTo('/about')" class="btn btn-outline">About Me</button>
          </div>
        </div>
      </div>
    </section>
    
    <section class="featured-works section bg-alt">
      <div class="container">
        <h2 class="section-title">Featured Works</h2>
        <div class="grid">
          <div v-for="work in featuredWorks" :key="work.id" class="card">
            <div class="card-image">
              <img :src="work.image" :alt="work.title">
            </div>
            <div class="card-content">
              <h3>{{ work.title }}</h3>
              <p class="text-secondary">{{ work.category }}</p>
            </div>
          </div>
        </div>
        <div class="view-all-button">
          <button @click="navigateTo('/portfolio')" class="btn">View All Works</button>
        </div>
      </div>
    </section>
  </div>
</template>

<style scoped>
.hero {
  text-align: center;
  padding: 6rem 1rem;
  background-color: var(--color-background);
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--color-primary);
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: var(--color-secondary);
}

.hero-text {
  font-size: 1.2rem;
  margin-bottom: 2rem;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.featured-works {
  padding: 5rem 0;
  background-color: var(--color-background-alt);
}

.card {
  background-color: var(--color-background);
  color: var(--color-text);
}

.card-image {
  height: 220px;
  overflow: hidden;
  background-color: #f0f0f0;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.card:hover .card-image img {
  transform: scale(1.05);
}

.view-all-button {
  text-align: center;
  margin-top: 3rem;
}

@media (max-width: 768px) {
  .hero {
    padding: 4rem 1rem;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.25rem;
  }
  
  .hero-buttons {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>




