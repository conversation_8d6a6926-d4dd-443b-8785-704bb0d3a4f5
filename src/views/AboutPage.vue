<script setup>
import appData from '@/stores/appData.js'

// Access the shared data
const { state } = appData
const artist = state.artist
</script>

<template>
  <div class="about-page">
    <section class="section">
      <div class="container">
        <h1 class="section-title">About Me</h1>
        
        <div class="about-content">
          <div class="profile-image">
            <img src="@/assets/images/profile-picture.jpg" alt="Artist profile">
          </div>
          
          <div class="bio">
            <p>{{ artist.bio }}</p>
            
            <p>
              My work has been featured in various online galleries and publications. I enjoy 
              bringing characters and stories to life through my art, combining traditional 
              artistic principles with modern digital techniques.
            </p>
            
            <h2>Skills</h2>
            <ul>
              <li>Digital Illustration</li>
              <li>Character Design</li>
              <li>Concept Art</li>
              <li>Digital Painting</li>
              <li>Storyboarding</li>
            </ul>
            
            <h2>Software</h2>
            <ul>
              <li>Adobe Photoshop</li>
              <li>Procreate</li>
              <li>Clip Studio Paint</li>
              <li>Adobe Illustrator</li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style scoped>
/* Only add specific styles not covered by global.css */
.about-page {
  background-image: url('@/assets/images/darkbackground1.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  min-height: 100vh;
}

h1 {
  color: var(--color-text-lighter);
}

.about-content {
  background-color: #2a2a2a;
  display: flex;
  gap: var(--spacing-lg);
  align-items: flex-start;
}

.profile-image {
  flex: 0 0 300px;
}

.profile-image img {
  width: 100%;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
  object-fit: cover;
}

.bio {
  flex: 1;
}

/* Use light text color variables for text content */
.bio p {
  color: var(--color-text-light);
}

.bio h2 {
  color: var(--color-text-lighter);
}

.bio ul {
  color: var(--color-text-light);
}

ul {
  padding-left: 1.5rem;
  margin-bottom: var(--spacing-md);
}

li {
  margin-bottom: var(--spacing-sm);
}

@media (max-width: 768px) {
  .about-content {
    flex-direction: column;
  }

  .profile-image {
    flex: 0 0 auto;
    max-width: 250px;
    margin: 0 auto var(--spacing-lg);
  }
}
</style>


