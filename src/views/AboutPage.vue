<script setup>
import appData from '@/stores/appData.js'

// Access the shared data
const { state } = appData
const artist = state.artist
</script>

<template>
  <div class="about-page">
    <section class="section">
      <div class="container">
        <h1 class="section-title">About Me</h1>
        
        <div class="about-content">
          <div class="profile-image">
            <img src="@/assets/images/profile-picture.jpg" alt="Artist profile">
          </div>
          
          <div class="bio">
            <p>{{ artist.bio }}</p>
            
            <p>
              My work has been featured in various online galleries and publications. I enjoy 
              bringing characters and stories to life through my art, combining traditional 
              artistic principles with modern digital techniques.
            </p>
            
            <h2>Skills</h2>
            <ul>
              <li>Digital Illustration</li>
              <li>Character Design</li>
              <li>Concept Art</li>
              <li>Digital Painting</li>
              <li>Storyboarding</li>
            </ul>
            
            <h2>Software</h2>
            <ul>
              <li>Adobe Photoshop</li>
              <li>Procreate</li>
              <li>Clip Studio Paint</li>
              <li>Adobe Illustrator</li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style scoped>
/* Only add specific styles not covered by global.css */
.about-page {
  background-image: url('@/assets/images/darkbackground1.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  min-height: 100vh;
}

h1 {
  color: var(--color-text-lighter);
}

.about-content {
  background-color: rgba(42, 42, 42, 0.7); /* More transparent background */
  display: flex;
  gap: var(--spacing-lg);
  align-items: flex-start;
  backdrop-filter: blur(5px); /* Add subtle blur effect */
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
}

.profile-image {
  flex: 0 0 300px;
}

.profile-image img {
  width: 100%;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
  object-fit: cover;
}

.bio {
  flex: 1;
}

/* Use light text color variables for text content */
.bio p {
  color: var(--color-text-light);
  font-size: 1.1rem; /* Larger text size */
  font-weight: 500; /* Bolder font weight */
  line-height: 1.6; /* Better line spacing */
}

.bio h2 {
  color: var(--color-text-lighter);
  font-size: 1.8rem; /* Larger heading size */
  font-weight: 700; /* Bolder font weight */
  margin-bottom: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.bio ul {
  color: var(--color-text-light);
  font-size: 1.1rem; /* Larger text size for list items */
  font-weight: 500; /* Medium font weight for better readability */
}

ul {
  padding-left: 1.5rem;
  margin-bottom: var(--spacing-md);
}

li {
  margin-bottom: var(--spacing-sm);
  font-weight: 500; /* Bolder list items */
  line-height: 1.5; /* Better line spacing */
}

@media (max-width: 768px) {
  .about-content {
    flex-direction: column;
  }

  .profile-image {
    flex: 0 0 auto;
    max-width: 250px;
    margin: 0 auto var(--spacing-lg);
  }
}
</style>


