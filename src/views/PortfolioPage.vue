<script setup>
import appData from '@/stores/appData.js'

// Access the shared data
const { state } = appData
const portfolioItems = state.portfolioItems
</script>

<template>
  <div class="portfolio-page">
    <section class="section">
      <div class="container">
        <h1 class="section-title">Portfolio</h1>
        
        <!-- Portfolio grid using global grid styles -->
        <div class="grid">
          <div v-for="item in portfolioItems" :key="item.id" class="card">
            <img :src="item.image" :alt="item.title">
            <div class="card-content">
              <h3>{{ item.title }}</h3>
              <p class="text-secondary">{{ item.category }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style scoped>
/* Only add specific styles not covered by global.css */
.card {
  background-color: #2a2a2a;
  border: 1px solid #3a3a3a;
  border-radius: var(--border-radius-md);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  border-color: var(--color-primary);
}

.card-content {
  padding: 1.2rem;
}

.card-content h3 {
  color: var(--color-primary);
  margin-bottom: 0.5rem;
}

.text-secondary {
  color: #b0b0b0;
  font-size: 0.9rem;
}

img {
  width: 100%;
  height: 220px;
  object-fit: cover;
  display: block;
  border-bottom: 1px solid #3a3a3a;
}
</style>

