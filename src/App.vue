<script setup>
import NavBar from './components/NavBar.vue'
import Footer from './components/Footer.vue'
</script>

<template>
  <div class="app-container">
    <NavBar />
    <main>
      <router-view />
    </main>
    <Footer />
  </div>
</template>

<style>
/* Override any conflicting styles from main.css */
#app {
  max-width: none;
  margin: 0;
  padding: 0;
  font-weight: normal;
  display: block;
}

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--color-background);
  color: var(--color-text);
}

main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

@media (min-width: 1024px) {
  body {
    display: block;
  }

  #app {
    display: block;
    padding: 0;
  }
}
</style>


