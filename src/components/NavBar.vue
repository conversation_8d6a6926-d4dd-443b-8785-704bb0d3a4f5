<script setup>
import { useRouter } from 'vue-router'
import appData from '@/stores/appData.js'

const router = useRouter()
const { state } = appData
const siteName = state.siteConfig.siteName

const navItems = [
  { name: 'Home', path: '/' },
  { name: 'Portfolio', path: '/portfolio' },
  { name: 'About', path: '/about' }
]

const navigateTo = (path) => {
  router.push(path)
}
</script>

<template>
  <nav class="navbar">
    <div class="container">
      <div class="navbar-content">
        <div class="logo">
          <router-link to="/">
            {{ siteName }}
          </router-link>
        </div>
        <div class="nav-links">
          <a 
            v-for="item in navItems" 
            :key="item.name" 
            @click="navigateTo(item.path)"
            class="nav-link"
            :class="{ 'router-link-active': $route.path === item.path }"
            href="#"
          >
            {{ item.name }}
          </a>
        </div>
      </div>
    </div>
  </nav>
</template>

<style scoped>
.navbar {
  background-color: var(--color-background);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-md) 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.navbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo a {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-primary);
  text-decoration: none;
}

.nav-links {
  display: flex;
  gap: var(--spacing-lg);
}

.nav-link {
  font-weight: 500;
  color: var(--color-text);
  text-decoration: none;
  transition: color var(--transition-fast);
  padding: 0.5rem 0;
  position: relative;
  cursor: pointer;
}

.nav-link:after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: var(--color-primary);
  transition: width var(--transition-normal);
}

.nav-link:hover:after, 
.router-link-active:after {
  width: 100%;
}

.nav-link:hover, 
.router-link-active {
  color: var(--color-primary);
}

@media (max-width: 768px) {
  .navbar-content {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .nav-links {
    gap: var(--spacing-md);
  }
}
</style>






