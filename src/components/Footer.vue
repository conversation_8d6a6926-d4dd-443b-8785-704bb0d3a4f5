<script setup>
import appData from '@/stores/appData.js'

const { state } = appData
const { footerText, showSocialLinks } = state.siteConfig
const { social } = state.artist

const currentYear = new Date().getFullYear()
</script>

<template>
  <footer class="footer">
    <div class="container">
      <div v-if="showSocialLinks" class="social-links">
        <a v-if="social.instagram" :href="social.instagram" target="_blank" aria-label="Instagram" class="social-link">
          Instagram
        </a>
        <a v-if="social.twitter" :href="social.twitter" target="_blank" aria-label="Twitter" class="social-link">
          Twitter
        </a>
        <a v-if="social.artstation" :href="social.artstation" target="_blank" aria-label="ArtStation" class="social-link">
          ArtStation
        </a>
      </div>
      <p class="copyright">{{ footerText.replace('2023', currentYear) }}</p>
    </div>
  </footer>
</template>

<style scoped>
.footer {
  background-color: var(--color-background-alt);
  padding: var(--spacing-lg) 0;
  text-align: center;
  margin-top: auto;
  width: 100%;
  border-top: 1px solid var(--color-border);
}

.social-links {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
}

.social-link {
  color: var(--color-text-dark);
  transition: color var(--transition-fast);
}

.social-link:hover {
  color: var(--color-primary);
}

.copyright {
  color: var(--color-text-light);
  font-size: 0.9rem;
  margin: 0;
}
</style>


