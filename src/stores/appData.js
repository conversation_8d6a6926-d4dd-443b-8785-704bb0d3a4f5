import { reactive, readonly } from 'vue'

// Create a reactive data store
const state = reactive({
  // Artist information
  artist: {
    name: '<PERSON><PERSON><PERSON>',
    title: 'Digital Artist & Illustrator',
    bio: 'Creative digital artist specializing in character design and illustration.',
    email: '<EMAIL>',
    social: {
      instagram: 'https://instagram.com/toonsy_sa',
      twitter: '',
      artstation: ''
    }
  },
  
  // Portfolio items - can be used across pages
  portfolioItems: [
    { 
      id: 1, 
      title: 'Character Designs', 
      image: '/src/assets/images/IMG_1094.jpg', 
      category: 'character' 
    },
    { 
      id: 2, 
      title: 'Comics', 
      image: '/src/assets/images/IMG_1236.jpg', 
      category: 'comics' 
    },
    { 
      id: 3, 
      title: 'Animal Art', 
      image: '/src/assets/images/IMG_1248.jpg', 
      category: 'character' 
    }
  ],
  
  // Site configuration
  siteConfig: {
    siteName: 'Toonsy-SA',
    footerText: '© 2023 Artist Name. All rights reserved.',
    showSocialLinks: true,
    primaryColor: '#fae361ff', // Changed from Vue green to gold
    secondaryColor: '#35495e'
  }
})

// Actions to modify the state
const actions = {
  // Update artist information
  updateArtistInfo(newInfo) {
    Object.assign(state.artist, newInfo)
  },
  
  // Add a portfolio item
  addPortfolioItem(item) {
    const newId = Math.max(0, ...state.portfolioItems.map(p => p.id)) + 1
    state.portfolioItems.push({ ...item, id: newId })
  },
  
  // Remove a portfolio item
  removePortfolioItem(id) {
    const index = state.portfolioItems.findIndex(item => item.id === id)
    if (index !== -1) {
      state.portfolioItems.splice(index, 1)
    }
  },
  
  // Update site configuration
  updateSiteConfig(config) {
    Object.assign(state.siteConfig, config)
  }
}

// Export readonly state and actions
export default {
  state: readonly(state),
  ...actions
}



